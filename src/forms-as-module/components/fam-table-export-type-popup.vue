<script setup>
// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  subtitle: {
    type: String,
    required: true,
  },
});

// ----------------------------- Emits - Events ------------------------------ //
defineEmits(['close', 'save']);

// ------------------------ Variables - Local - refs ------------------------ //
const form_data = ref({});
</script>

<template>
  <HawkModalContainer content_class="rounded-lg !w-[800px]">
    <HawkModalHeader @close="$emit('close')">
      <template #title>
        {{ props.title }}
      </template>
      <template #subtitle>
        {{ props.subtitle }}
      </template>
    </HawkModalHeader>
    <HawkModalContent>
      <Vueform
        v-model="form_data"
        :display-errors="false"
        :sync="true"
      >
        <RadiogroupElement
          view="blocks"
          :add-classes="{
            RadiogroupElement: {
              wrapper: '-mt-3',
            },
            RadiogroupRadio: {
              wrapper: '!py-2',
              text: 'text-sm',
              description: 'text-xs',
            },
          }"
          class="export-type-radio-grp-btn"
          default="displayed_data"
          :columns="{
            default: { container: 12, label: 3, wrapper: 12 },
            sm: { container: 12, label: 3, wrapper: 6 },
            md: { container: 12, label: 3, wrapper: 9 },
          }"
          name="export" :label="$t('Export')" :items="[
            { value: 'displayed_data', label: $t('Displayed data'), description: $t('Export only the data currently visible in the table - including applied filters, selected columns, and conditional formatting.') },
            { value: 'all_data', label: $t('All data'), description: $t('Export the complete data with all columns. Filter, column selections, and conditional formatting will not be applied.') },
          ]"
        />
      </Vueform>
    </HawkModalContent>
    <HawkModalFooter class="flex items-center" :class="[true ? 'justify-between' : 'justify-end']">
      <template #right>
        <div class="flex items-center">
          <HawkButton
            class="mr-3"
            type="outlined"
            @click="$emit('close')"
          >
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            color="primary"
            @click="$emit('save', form_data)"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </HawkModalFooter>
  </HawkModalContainer>
</template>

<style lang="scss">
.export-type-radio-grp-btn {
  .form-border-width-input {
    border: 0 !important;
    padding-left:  0!important;
    padding-right:  0!important;
  }

  .form-bg-selected {
    color: black !important;
    background-color: transparent !important;
  }
}
</style>
