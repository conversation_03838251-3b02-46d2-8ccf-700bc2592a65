<script setup>
import { storeToRefs } from 'pinia';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const $t = inject('$t');

const project_management_store = useProjectManagementStore();

const { $g, active_schedule, active_view } = storeToRefs(project_management_store);

const { modify_config, set_view_dirtiness } = project_management_store;

function setBaseline(baseline) {
  modify_config({ key: 'baseline', value: baseline.uid });
  set_view_dirtiness(true);
  $g.value.render();
}

const baseline_menu_items = computed(() => {
  if (active_schedule.value?.baselines?.length === 0)
    return [];
  const arr = [];
  active_schedule.value.baselines.forEach((baseline) => {
    arr.push({
      uid: baseline.uid,
      label: baseline.name,
      on_click: () => setBaseline(baseline),
    });
  });

  return arr;
});
</script>

<template>
  <hawk-menu
    :items="baseline_menu_items"
    :active_item="active_view.data.baseline"
    additional_dropdown_classes="right-full !-top-0 mr-4 !mt-0 !bottom-auto"
    additional_trigger_classes="!ring-0"
    position="bottom-left"
  >
    <template #trigger>
      <div class="flex items-center h-9 hover:bg-gray-50 rounded-lg py-2 px-3 -ml-3 w-[232px]">
        <div class="text-sm text-ellipsis overflow-hidden relative whitespace-nowrap">
          <span class="text-gray-500">{{ $t('Baseline') }}: </span>
          {{ active_schedule?.baselines?.find((baseline) => baseline.uid === active_view.data.baseline)?.name }}
        </div>
        <IconHawkChevronRight class="ml-auto" />
      </div>
    </template>
  </hawk-menu>
</template>
