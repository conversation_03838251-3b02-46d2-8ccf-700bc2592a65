<script setup>
import { storeToRefs } from 'pinia';
import { useModal } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import PmBaselineDetailsModal from '~/project-management/components/menus/pm-baseline-details-modal.vue';
import PmEditBaselineModal from '~/project-management/components/menus/pm-edit-baseline-modal.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const emit = defineEmits(['close']);

const $t = inject('$t');
const $date = inject('$date');

const project_management_store = useProjectManagementStore();
const { active_schedule } = storeToRefs(project_management_store);
const { get_baselines, delete_baseline } = project_management_store;

const state = reactive({
  is_loading: false,
  table_data: [],
  freeze_table: -1,
});

const table_columns = [
  {
    accessorKey: 'name',
    header: $t('Name'),
    id: 'name',
  },
  {
    accessorKey: 'description',
    header: $t('Description'),
    id: 'description',
  },
  {
    accessorKey: 'created_at',
    header: $t('Created on'),
    id: 'created_at',
  },
  {
    accessorKey: 'created_by',
    header: $t('Created by'),
    id: 'created_by',
  },
  {
    accessorKey: 'context_menu',
    header: '',
    id: 'context_menu',
    size: '5',
    show_on_hover: 'true',
  },
];

const delete_popup = useModal({
  component: HawkDeletePopup,
  attrs: {
    onClose() {
      delete_popup.close();
    },
  },
});

const view_baseline_modal = useModal({
  component: PmBaselineDetailsModal,
  attrs: {
    onClose() {
      view_baseline_modal.close();
    },
  },
});

const edit_baseline_modal = useModal({
  component: PmEditBaselineModal,
  attrs: {
    onClose() {
      edit_baseline_modal.close();
    },
    onSave(baseline) {
      setBaselines();
      edit_baseline_modal.close();
      if (baseline) {
        viewBaseline(baseline);
      }
    },
  },
});

function createBaseline() {
  edit_baseline_modal.patchOptions({
    attrs: {
      mode: 'create',
      baseline: {},
    },
  });
  edit_baseline_modal.open();
}

function viewBaseline(baseline) {
  view_baseline_modal.patchOptions({
    attrs: {
      baseline,
    },
  });
  view_baseline_modal.open();
}

function editBaseline(baseline) {
  edit_baseline_modal.patchOptions({
    attrs: {
      mode: 'edit',
      baseline,
    },
  });
  edit_baseline_modal.open();
}

function deleteBaseline(baseline) {
  delete_popup.patchOptions({
    attrs: {
      header: `${$t('Delete baseline')} - ${baseline.name}`,
      content: $t('Are you sure you want to delete this baseline?'),
      confirm: async () => {
        await delete_baseline({ uids: [baseline.uid] });
        state.table_data = state.table_data.filter(b => b.uid !== baseline.uid);
        delete_popup.close();
      },
    },
  });
  delete_popup.open();
}

async function setBaselines() {
  state.is_loading = true;
  state.table_data = await get_baselines();
  state.is_loading = false;
}

onMounted(async () => {
  state.table_data = active_schedule.value.baselines;
});
</script>

<template>
  <HawkModalContainer
    content_class="rounded-none w-full h-full"
    :options="{ escToClose: false }"
  >
    <Vueform
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ $t('Manage baselines') }}
            </div>
          </template>
          <template #right>
            <HawkButton class="ml-7" @click="createBaseline">
              <IconHawkPlus />
              {{ $t("New baseline") }}
            </HawkButton>
          </template>
        </HawkModalHeader>
        <HawkModalContent class="!h-[calc(100vh-160px)] !max-h-[calc(100vh-160px)] pm-set-planned-values-content">
          <HawkLoader v-if="state.is_loading" container_class="h-full" />
          <HawkTable
            v-else
            :data="state.table_data"
            :columns="table_columns"
            :show_menu_header="false"
            :disable_resize="true"
            :freeze_table="state.freeze_table"
            is_gapless
            @row-clicked="row => viewBaseline(row)"
          >
            <template #description="{ data }">
              {{ data.getValue() || '-' }}
            </template>
            <template #created_at="{ data }">
              {{ $date(data.getValue(), 'DD MMMM YYYY, hh:mma') }}
            </template>
            <template #created_by="{ data }">
              <HawkMembers :members="data.getValue().uid" type="badge" />
            </template>
            <template #context_menu="data">
              <HawkMenu
                :items="[
                  {
                    label: $t('Edit'),
                    value: 'edit',
                    on_click: () => {
                      editBaseline(data.data.row.original);
                    },
                  },
                  {
                    label: $t('Delete'),
                    value: 'delete',
                    on_click: () => {
                      deleteBaseline(data.data.row.original);
                    },
                  },
                ]"
                position="fixed"
                additional_trigger_classes="!ring-0 !flex !items-center"
                @click.stop=""
                @open="state.freeze_table = data.data?.row?.id"
                @close="state.freeze_table = '-1'"
              >
                <template #trigger>
                  <IconHawkDotsVertical class="flex items-center text-gray-600" />
                </template>
              </HawkMenu>
            </template>
          </HawkTable>
        </HawkModalContent>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
