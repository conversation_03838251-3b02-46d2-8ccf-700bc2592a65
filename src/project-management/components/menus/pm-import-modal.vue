<!-- eslint-disable vue/prop-name-casing -->
<script setup>
import dayjs from 'dayjs';
import DOMPurify from 'dompurify';
import { isNil, keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { inject } from 'vue';
import { csvInjectionProtector, sleep } from '~/common/utils/common.utils.js';
import { CONVERSION_RATES } from '~/project-management/constants/pm-constants.js';
import { useProjectManagementStore } from '~/project-management/store/pm.store.js';
import { generateUID } from '~/project-management/utils/pm-helper.utils';

const emit = defineEmits(['close']);
const $toast = inject('$toast');
const $t = inject('$t');
const project_management_store = useProjectManagementStore();

const { $g, active_schedule } = storeToRefs(project_management_store);

const {
  modify_config,
  preprocess_activity,
  update_active_schedule,
} = project_management_store;

const state = reactive({
  is_importing: false,
  is_exporting: false,
});

async function downloadTemplate() {
  if (state.is_exporting)
    return;
  state.is_exporting = true;

  await sleep(500); // https://uxmag.com/articles/let-your-users-wait

  const ExcelJS = await import('exceljs');
  const { saveAs } = await import('file-saver');
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Schedule');
  worksheet.columns = [
    { header: $t('WBS'), key: 'wbs', width: 10 },
    { header: $t('ID'), key: 'id', width: 10 },
    { header: $t('Activity'), key: 'activity', width: 30 },
    { header: $t('Planned Start'), key: 'planned_start', width: 15 },
    { header: $t('Duration'), key: 'duration', width: 10 },
    { header: $t('Predecessors'), key: 'predecessors', width: 15 },
    ...(active_schedule.value.has_activity_weightages ? [{ header: $t('Weight'), key: 'weight', width: 10 }] : []),
  ];
  const data = [];
  const links = $g.value.getLinks();

  $g.value.eachTask((task) => {
    if (task.type === $g.value.config.types.surrogate)
      return;

    const predecessors = links
      .filter(link => link.target === task.id)
      .map(link => `${csvInjectionProtector(link.source)} ${link.type}${link.lag ? `${link.lag < 0 ? '-' : '+'}${Math.abs(link.lag)} days` : ''}`)
      .join(', ');

    data.push({
      wbs: $g.value.getWBSCode(task),
      id: csvInjectionProtector(task.id),
      activity: csvInjectionProtector(task.text),
      planned_start: dayjs(task.start_date).format('DD MMMM YYYY'),
      duration: task.duration,
      predecessors,
      ...(active_schedule.value.has_activity_weightages ? { weight: task.weight } : {}),
    });
  });
  worksheet.addRows(data);
  const buffer = await workbook.xlsx.writeBuffer();
  saveAs(new Blob([buffer]), `template - ${active_schedule.value.name}.xlsx`);
  state.is_exporting = false;
}

function getType({ duration, wbs }, all_wbs_codes) {
  if (wbs.split('.').length === 1)
    return $g.value.config.types.project;
  if (all_wbs_codes.includes(`${wbs}.1`))
    return $g.value.config.types.wbs;
  if (duration === 0)
    return $g.value.config.types.milestone;
  return $g.value.config.types.task;
}

function getParent(wbs, data) {
  if (!wbs || wbs.split('.').length <= 1)
    return $g.value.config.root_id;

  const parent_wbs = wbs.split('.').slice(0, -1).join('.');
  const parent_task = data.find(item => item.wbs === parent_wbs);
  return parent_task?.id || $g.value.config.root_id;
}

function detectMissingParents(all_wbs_codes) {
  const missing_parents = [];
  for (const wbs_code of all_wbs_codes) {
    const parent_wbs = wbs_code.split('.').slice(0, -1).join('.');
    if (parent_wbs && !all_wbs_codes.includes(parent_wbs))
      missing_parents.push(wbs_code);
  }
  return missing_parents;
}

function getLinkProperties(text, all_ids) {
  const link = {
    uid: generateUID(),
    type: 'FS',
    lag: 0,
    source: null,
  };

  const parts_of_text = text.split(' ');
  const predecessor_id = parts_of_text?.[0];
  if (!all_ids.includes(predecessor_id))
    return link; // the text does not contain any valid ID, so return with source set as null

  link.source = predecessor_id;

  if (parts_of_text.length > 1)
    text = parts_of_text.slice(1, parts_of_text.length).join('');
  else
    return link; // the text only contains the ID, so there's no point in parsing further

  if (text.match(/^(FS|SS|FF|SF)/i)) {
    link.type = (text.match(/^(FS|SS|FF|SF)/i)[0]).toUpperCase();
    text = text.replace(link.type, '').trim();
  }

  if (text.startsWith('+') || text.startsWith('-')) {
    const lag_value = text.match(/[-+]\d+/)[0];
    link.lag = Number.parseFloat(lag_value);
    text = text.replace(lag_value, '').trim();
  }
  link.lag = !link.lag || Number.isNaN(link.lag) ? 0 : link.lag;

  const pattern = /(days*|weeks*|months*|years*|[dwmy])?/g;
  if (text.match(pattern)) {
    const unit = text.match(pattern)[0];
    if (unit in CONVERSION_RATES)
      link.lag = link.lag * CONVERSION_RATES[unit];
  }
  link.lag = Math.ceil(link.lag);

  return link;
}

async function ingestSpreadsheetData(data) {
  state.is_importing = true;
  const ExcelJS = await import('exceljs');
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(data);
  const worksheet = workbook.worksheets[0];
  const rows = worksheet.getSheetValues();

  const all_ids = [];
  const all_wbs_codes = [];
  const processed_data = {
    data: [],
    links: [],
  };

  const id_uid_map = {};
  $g.value.eachTask((task) => {
    if (task.type === $g.value.config.types.surrogate)
      return;
    id_uid_map[task.id] = task.uid;
  });

  const xlsx_data = [];

  const useful_rows = rows.slice(2);

  if (!useful_rows.length) {
    return handleError($t('No data found'));
  }

  for (const [row_index, raw_row] of useful_rows.entries()) {
    try {
      if (!raw_row?.some(Boolean))
        continue;

      const row = raw_row.map(item => DOMPurify.sanitize(item?.toString?.() ?? '', { ALLOWED_TAGS: [] }));
      // NOTE: this row may still (and likely does) contain empty values as map skips them;
      // so don't remove the optional chaining before the row?.[x]?.function calls in the following code

      const extracted_wbs = row?.[1]?.trim();
      if (!/^\d+(?:\.\d+)*$/.test(extracted_wbs)) {
        return handleError(`${$t('Invalid WBS code in row')} ${row_index + 2}`);
      }

      const extracted_id = row?.[2]?.trim();
      if (!extracted_id) {
        return handleError(`${$t('Missing ID in row')} ${row_index + 2}`);
      }

      const extracted_text = row?.[3]?.trim();
      if (!extracted_text) {
        return handleError(`${$t('Missing activity name in row')} ${row_index + 2}`);
      }

      const extracted_planned_start = row?.[4]?.trim();
      const extracted_duration = Number.parseInt(row?.[5]);

      // 'predecessors' and 'weight' are optional fields, so no error handling is needed for them
      const extracted_predecessors = (row?.[6]?.split(',') || []).map(item => item.trim());
      const extracted_weight = Number.parseFloat(row?.[7]);

      all_ids.push(extracted_id);
      all_wbs_codes.push(extracted_wbs);

      xlsx_data.push({
        id: extracted_id,
        wbs: extracted_wbs,
        text: extracted_text,
        duration: extracted_duration,
        planned_duration: extracted_duration,
        planned_start: extracted_planned_start ? new Date(extracted_planned_start) : null,
        predecessors: extracted_predecessors.filter(Boolean),
        weight: Number.isNaN(extracted_weight) ? null : extracted_weight,
        row_index: row_index + 2, // NOTE: Just for error reporting
      });
    }
    catch (error) {
      console.error(error);
      return handleError(`${$t('Error in row')} ${row_index + 2}`);
    }
  }

  for (const activity of xlsx_data) {
    const task_type = getType(activity, all_wbs_codes);

    if (task_type === $g.value.config.types.task || task_type === $g.value.config.types.milestone) {
      if (!activity.planned_start || !dayjs(activity.planned_start).isValid()) {
        return handleError(`${$t('Invalid planned start date in row')} ${activity.row_index}`);
      }

      if (Number.isNaN(activity.duration)) {
        return handleError(`${$t('Invalid duration in row')} ${activity.row_index}`);
      }
    }
  }

  const missing_parents = detectMissingParents(all_wbs_codes);
  if (missing_parents.length) {
    return handleError(`${$t('Missing parent activities')} ― ${missing_parents.join(', ')}`);
  }

  const root_level_tasks = xlsx_data.filter(task => !task.wbs.includes('.'));
  if (root_level_tasks.length > 1) {
    return handleError($t('Multiple project tasks found. Only one project task is allowed.'));
  }

  for (const activity of xlsx_data) {
    const activity_wbs_parts = activity.wbs.split('.');
    const activity_ancestors = [];

    let ancestor_wbs = '';
    for (const part of activity_wbs_parts.slice(0, -1)) {
      ancestor_wbs = ancestor_wbs ? `${ancestor_wbs}.${part}` : part;
      activity_ancestors.push(ancestor_wbs);
    }

    for (const predecessor of activity.predecessors) {
      const predecessor_task = xlsx_data.find(task => task.id === predecessor);
      if (predecessor_task && activity_ancestors.includes(predecessor_task.wbs)) {
        return handleError(`${activity.text} ${$t('cannot have its ancestor')} ${predecessor_task.text} ${$t('as a predecessor')}`);
      }
    }
  }

  const are_all_weights_null = xlsx_data.every(activity => activity.weight === null);

  if (!are_all_weights_null && !active_schedule.value.has_activity_weightages) {
    await update_active_schedule(
      active_schedule.value.uid,
      { has_activity_weightages: true },
      true,
    );
  }

  // if there are any duplicate IDs found, show an error and return
  if (all_ids.length !== new Set(all_ids).size) {
    return handleError($t('Duplicate IDs found'));
  }

  for (const [index, activity] of xlsx_data.entries()) {
    for (const predecessor of activity.predecessors) {
      const link = getLinkProperties(predecessor, all_ids);

      if (link.source === null) {
        return handleError(`${$t('Invalid predecessor in row')} ${index + 2}`);
      }

      if (link.source === activity.id) {
        return handleError(`Self-referential predecessor in row ${index + 2}`);
      }

      processed_data.links.push({
        ...link,
        target: activity.id,
      });
    }

    const existing_activity_data = active_schedule.value.activities[id_uid_map[activity.id]];
    if (existing_activity_data) {
      delete existing_activity_data.end_date;
      delete existing_activity_data.planned_finish;
    }

    processed_data.data.push(preprocess_activity({
      ...(existing_activity_data || {}),
      ...activity,
      type: getType(activity, all_wbs_codes),
      uid: id_uid_map[activity.id] || generateUID(),
      parent: getParent(activity.wbs, xlsx_data),
      weight: isNil(activity.weight) ? null : activity.weight / 100,
      is_backend_save_pending: id_uid_map[activity.id]
        ? !!existing_activity_data.is_backend_save_pending
        : true,
    }, false));
  };

  // remove all duplicate links in processed_data.links by checking if exact same source and target are found in two or more links
  processed_data.links = processed_data.links.filter((link, index) => {
    return processed_data.links.findIndex(l => l.source === link.source && l.target === link.target) === index;
  });

  $g.value.clearAll();
  $g.value.parse(processed_data);
  active_schedule.value.activities = keyBy(processed_data.data, 'uid');
  active_schedule.value.relations = processed_data.links;

  modify_config({ key: 'wbs_level', value: Number.MAX_SAFE_INTEGER });
  modify_config({ key: 'wbs_level_max', value: Number.MAX_SAFE_INTEGER });

  $g.value.batchUpdate(() => {
    $g.value.eachTask((task) => {
      $g.value.open(task.id);
    });
  });
  $g.value.render();

  state.is_importing = false;
  emit('close');
}

function submitHandler(form$) {
  try {
    const file = form$.data.imported_file;
    if (!file?.name?.endsWith('.xlsx')) {
      return handleError($t('Unsupported file'));
    }
    const reader = new FileReader();
    reader.onload = e => ingestSpreadsheetData(e.target.result);
    reader.readAsArrayBuffer(file);
  }
  catch (err) {
    emit('close');
    logger.error(err);
    handleError($t('Failed to import your data'));
  }
}

function handleError(message) {
  $toast({
    title: $t('Error'),
    text: message,
    type: 'error',
    timeout: 4000,
  });
  logger.error(message);
  state.is_importing = false;
}
</script>

<template>
  <HawkModalContainer content_class="w-64 rounded-lg">
    <Vueform
      :display-errors="false"
      size="sm"
      :should_validate_on_mount="false"
      @submit="submitHandler"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #left>
            {{ $t('Import') }}
          </template>
        </HawkModalHeader>
        <HawkModalContent class="max-h-96 scrollbar">
          <StaticElement name="static">
            <div class="text-gray-600">
              {{ $t("Download a pre-formatted XLSX file to use as a template for your import.") }}
            </div>
            <HawkButton :loading="state.is_exporting" type="outlined" class="text-gray-600 font-semibold mt-3" @click="downloadTemplate">
              <IconHawkDownloadOne />
              {{ $t('Download template') }}
            </HawkButton>
            <div class="border-t border-t-gray-200 my-4" />
          </StaticElement>
          <FileElement
            name="imported_file"
            :presets="['hawk_file_element']"
            :options="{
              clickable_text: $t('Click to import'),
              text: $t('or drag and drop'),
            }"
            accept=".xlsx"
            :use_uppy="false"
            :drop="true"
            :auto="false"
            :submit="false"
            :clickable="false"
            :url="false"
            :rules="['required']"
            :messages="{ required: 'This field is required.' }"
          />
          <div class="text-xs text-warning-700 mt-3 flex items-center">
            <IconHawkAlertTriangle class="w-3 h-3 mr-1" />
            {{ $t('Importing will overwrite your existing data') }}
          </div>
        </HawkModalContent>
        <HawkModalFooter class="flex justify-between items-center">
          <template #right>
            <div class="flex justify-end">
              <HawkButton type="outlined" class="mr-3 font-semibold" @click="emit('close')">
                {{ $t('Cancel') }}
              </HawkButton>
              <ButtonElement
                submits
                size="sm"
                name="submit"
                :button-label="$t('Import')"
                :loading="state.is_importing"
              />
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
