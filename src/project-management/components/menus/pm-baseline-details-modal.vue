<script setup>
import dayjs from 'dayjs';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  baseline: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const $t = inject('$t');

const project_management_store = useProjectManagementStore();
const { get_baseline } = project_management_store;

const state = reactive({
  is_loading: false,
  baseline_data: null,
  hot_instance: null,
  hot_columns: [],
  hot_data: [],
  longest_wbs_code_length: 0,
});

const col_headers = computed(() => {
  return state.hot_columns.map((column, index) => {
    if (index <= 2)
      return column.header;
    else
      return dayjs(column.header, 'YYYY-MM').format('MMMM YYYY');
  });
});

function generateWBS(nodes, prefix = '') {
  let counter = 1;
  nodes.forEach((node) => {
    node.wbs = prefix ? `${prefix}.${counter}` : `${counter}`;
    state.longest_wbs_code_length = Math.max(state.longest_wbs_code_length, node.wbs.length);
    counter++;
    if (node.__children && node.__children.length > 0) {
      generateWBS(node.__children, node.wbs);
    }
  });
}

function rowHeaders(row) {
  const row_data = state.hot_instance?.getSourceDataAtRow(row);
  return row_data?.wbs || '';
}

function buildHierarchy(activities) {
  const map = {};
  const roots = [];
  activities.forEach((activity) => {
    map[activity.activity] = { ...activity, __children: [] };
  });
  activities.forEach((activity) => {
    if (activity.parent && map[activity.parent]) {
      map[activity.parent].__children.push(map[activity.activity]);
    }
    else {
      roots.push(map[activity.activity]);
    }
  });
  return roots;
}

function prepareHotData(nodes, all_months_set) {
  return nodes.map((node) => {
    const monthly_progress = {};
    if (node.scheduled_progress_completion) {
      Object.entries(node.scheduled_progress_completion).forEach(([date, value]) => {
        const month = date.slice(0, 7); // 'YYYY-MM'
        all_months_set.add(month);
        monthly_progress[month] = (monthly_progress[month] || 0) + (value * 100);
      });
    }
    const row = {
      wbs: node.wbs,
      text: node.name,
      planned_start: node.start_date || '',
      planned_finish: node.end_date || '',
      ...monthly_progress,
    };
    if (node.__children && node.__children.length > 0) {
      row.__children = prepareHotData(node.__children, all_months_set);
    }
    return row;
  });
}

onMounted(async () => {
  state.is_loading = true;
  state.baseline_data = await get_baseline(props.baseline.uid);
  const all_months_set = new Set();
  const activity_map = {};
  if (state.baseline_data?.activities?.length) {
    state.baseline_data.activities.forEach((activity) => {
      activity_map[activity.activity] = activity;
    });
  }

  const hierarchy = buildHierarchy(state.baseline_data?.activities || []);
  generateWBS(hierarchy);
  const hot_data = prepareHotData(hierarchy, all_months_set);

  const months = Array.from(all_months_set).sort();
  state.hot_columns = [
    {
      data: 'text',
      header: $t('Activity'),
      readOnly: true,
    },
    {
      data: 'planned_start',
      header: $t('Planned start'),
      readOnly: true,
      renderer: (_instance, td, _row, _col, _prop, value) => {
        td.textContent = value ? dayjs(value).format('DD MMMM YYYY') : '-';
      },
    },
    {
      data: 'planned_finish',
      header: $t('Planned finish'),
      readOnly: true,
      renderer: (_instance, td, _row, _col, _prop, value) => {
        td.textContent = value ? dayjs(value).format('DD MMMM YYYY') : '-';
      },
    },
    ...months.map(month => ({
      data: month,
      header: month,
      type: 'numeric',
      readOnly: true,
      renderer: (_instance, td, _row, _col, _prop, value) => {
        td.textContent = value !== undefined && value !== null ? `${Math.round(Number(value))}%` : '-';
      },
    })),
  ];
  state.hot_data = hot_data;
  state.is_loading = false;
});
</script>

<template>
  <HawkModalContainer
    content_class="rounded-none w-full h-full"
  >
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="flex flex-col justify-start">
            {{ $t('Baseline') }} - {{ props.baseline.name }}
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent class="!h-[calc(100vh-100px)] !max-h-[calc(100vh-100px)] pm-set-planned-values-content">
        <HawkLoader v-if="state.is_loading" />
        <HawkHandsOnTable
          v-else
          :hot-settings="{
            rowHeaders,
            nestedRows: true,
            bindRowsWithHeaders: true,
            rowHeaderWidth: state.longest_wbs_code_length * 15,
          }"
          :columns="state.hot_columns"
          :data="state.hot_data"
          :read-only="true"
          :col-headers="col_headers"
          height="100%"
          @ready="state.hot_instance = $event"
        />
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
