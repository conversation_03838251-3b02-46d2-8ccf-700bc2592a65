<script setup>
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { storeToRefs } from 'pinia';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  activity: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['close']);

dayjs.extend(isSameOrBefore);

const $t = inject('$t');

const project_management_store = useProjectManagementStore();
const { $g } = storeToRefs(project_management_store);

const state = reactive({
  interval: 'weekly',
  range: 'this_week',
  hot_data: [],
  hot_instance: null,
});

function generateInitialData() {
  const dates = getDatesBetween(dayjs().startOf('month'), dayjs().endOf('month'));
  let activities = [];
  if ([$g.value.config.types.task, $g.value.config.types.milestone].includes(props.activity.type)) {
    activities = [props.activity];
  }
  else {
    activities = $g.value.getChildren(props.activity.id)
      .map(child => $g.value.getTask(child))
      .filter((child) => {
        return [$g.value.config.types.task, $g.value.config.types.milestone].includes(child.type);
      });
  }

  return activities.map((item) => {
    const row = {
      id: item.id,
      activity: item.text,
      resource: item.resource,
    };

    dates.forEach((date) => {
      row[`${date}_work_done`] = 0;
      row[`${date}_amount_spent`] = 0;
      row[`${date}_duration`] = 0;
      row[`${date}_items`] = 0;
    });

    return row;
  });
}

const interval_options = [
  ['daily', 'Daily'],
  ['weekly', 'Weekly'],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
}));

const range_options = [
  ['this_week', 'This week', dayjs().startOf('week'), dayjs().endOf('week')],
  ['this_month', 'This month', dayjs().startOf('month'), dayjs().endOf('month')],
  ['last_7_days', 'Last 7 days', dayjs().subtract(7, 'days'), dayjs()],
  ['last_14_days', 'Last 14 days', dayjs().subtract(14, 'days'), dayjs()],
  ['last_30_days', 'Last 30 days', dayjs().subtract(30, 'days'), dayjs()],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
  min_date: item[2],
  max_date: item[3],
}));

function getDatesBetween(min_date, max_date) {
  const dates = [];
  let current_date = min_date || dayjs(min_date);
  const end_date = max_date || dayjs(max_date);
  while (current_date.isSameOrBefore(end_date)) {
    dates.push(current_date.format('DD-MMM-YY'));
    current_date = current_date.add(1, 'day');
  }
  return dates;
}

const hot_columns = computed(() => {
  const current_range = range_options.find(option => option.value === state.range);
  const dates = getDatesBetween(current_range.min_date, current_range.max_date);

  const dateColumns = dates.map(date => ({
    header: date,
    columns: [
      {
        data: `${date}_work_done`,
        header: $t('Work done'),
        type: 'numeric',
        width: 80,
      },
      {
        data: `${date}_amount_spent`,
        header: $t('Amount spent'),
        type: 'numeric',
        width: 100,
      },
      {
        data: `${date}_duration`,
        header: $t('Duration'),
        type: 'numeric',
        width: 80,
      },
      {
        data: `${date}_items`,
        header: $t('Items'),
        type: 'numeric',
        width: 80,
      },
    ],
  }));

  return [
    {
      data: 'id',
      header: $t('ID'),
      width: 60,
      readOnly: true,
    },
    {
      data: 'activity',
      header: $t('Activity'),
      width: 120,
      readOnly: true,
    },
    {
      data: 'resource',
      header: $t('Resource'),
      width: 120,
    },
    ...dateColumns.flatMap(dateCol => dateCol.columns),
  ];
});

const hot_nested_headers = computed(() => {
  const current_range = range_options.find(option => option.value === state.range);
  const dates = getDatesBetween(current_range.min_date, current_range.max_date);

  const firstLevel = [
    $t('ID'),
    $t('Activity'),
    $t('Resource'),
    ...dates.map(date => ({ label: date, colspan: 4 })),
  ];

  const secondLevel = [
    '',
    '',
    '',
    ...dates.flatMap(() => [
      $t('Work done'),
      $t('Amount spent'),
      $t('Duration'),
      $t('Items'),
    ]),
  ];

  return [firstLevel, secondLevel];
});

function afterChange(changes, source) {
  logger.log('afterChange', changes, source);
}

function beforeKeyDown(event) {
  logger.log('beforeKeyDown', event);
}

function rowHeaders(row) {
  return row + 1;
}

function cellsConfiguration(_row, _col) {
  const cellProperties = {};
  return cellProperties;
}

function onHandsOnTableReady(hot_instance) {
  state.hot_instance = hot_instance;
}

onMounted(() => {
  state.hot_data = generateInitialData();
});
</script>

<template>
  <HawkModalContainer content_class="w-[80vw]">
    <HawkModalHeader @close="emit('close')">
      <template #title>
        {{ $t('Activity tracking') }}
      </template>
    </HawkModalHeader>
    <HawkModalContent>
      <div class="flex justify-between items-center mb-6">
        <div class="text-sm font-normal text-gray-900">
          {{ $t('Track work done, amount spent for the activity, or record for a particular resource.') }}
        </div>
        <div class="flex items-center gap-2">
          <HawkMenu
            position="fixed"
            :items="range_options"
            @select="state.range = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Range') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ range_options.find((option) => option.value === state.range)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
          <HawkMenu
            position="fixed"
            :items="interval_options"
            @select="state.interval = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Interval') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ interval_options.find((option) => option.value === state.interval)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
        </div>
      </div>
      <HawkHandsOnTable
        v-if="state.hot_data.length"
        :hot-settings="{
          rowHeaders,
          afterChange,
          beforeKeyDown,
          nestedRows: false,
          bindRowsWithHeaders: true,
          nestedHeaders: hot_nested_headers,
          cells: cellsConfiguration,
          fixedColumnsStart: 3,
          className: 'htMiddle',
          contextMenu: false,
          dropdownMenu: false,
          columnSorting: false,
          headerClassName: 'htCenter',
        }"
        :right-click-menu="{}"
        :data="state.hot_data"
        :columns="hot_columns"
        :columns-menu="{ items: {} }"
        class="pm-excel-modal"
        @ready="onHandsOnTableReady"
      />
      <div v-else class="text-sm font-semiBold w-full h-[240px] grid place-items-center">
        No root level activities present for this activity.
      </div>
    </HawkModalContent>
    <HawkModalFooter>
      <template #right>
        <div class="flex justify-end w-full col-span-full">
          <HawkButton
            type="outlined"
            class="mr-4"
            @click="emit('close')"
          >
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            color="primary"
            @click="emit('close')"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </HawkModalFooter>
  </HawkModalContainer>
</template>
