<script setup>
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { useNaavixStore } from '~/naavix/store/naavix.store';
import TerraCharts from '~/terra/components/charts/terra-charts.vue';
import { useExportTerraReport } from '~/terra/utils/terra-report-export.composable.js';
import { useThermCustomReport } from '~/therm/composables/therm-custom-report.js';

const { $t, $services, route, common_store, auth_store } = useCommonImports();
const { progressFiles } = useExportTerraReport();
const { uploadFiles } = useThermCustomReport();

const naavix_store = useNaavixStore();
const { flags } = storeToRefs(naavix_store);
const { module_initialization_complete, module_initialization_failed } = naavix_store;

const terra_charts_ref$ = ref(null);
const state = reactive({
  group_workflow_project: {},
  upload_status: 'not_started',
});

async function uploadChartsData(files) {
  await uploadFiles(files, 'terra');
  await $services.terra.post({
    attribute: `naavix-upload/report-upload/container/${route.params.id}`,
    body: {
      service: files.map(file => file.service_object),
      asset: common_store.active_asset?.name,
      organization: auth_store.current_organization?.name,
    },
  });
  logger.log('Files uploaded successfully', files);
}

watch(() => terra_charts_ref$.value?.getChartsData(), async (charts_data) => {
  if (charts_data?.is_loading === false) {
    try {
      const files = await progressFiles(charts_data.progress_data);
      await uploadChartsData(files);
      state.upload_status = 'completed';
      module_initialization_complete();
    }
    catch (error) {
      logger.log('Error generating charts data Excel:', error);
      state.upload_status = 'failed';
      const error_message = error.data?.error_code === 'NO_DATA' ? $t('No data found') : null;
      module_initialization_failed(error_message);
    }
  }
});

watch(() => flags.value.is_agent_setting_up, () => {
  if (flags.value.is_agent_setting_up)
    state.upload_status = 'started';
});
</script>

<template>
  <div v-if="state.upload_status === 'started'" style="position: absolute; top: -9999px; left: -9999px;">
    <TerraCharts ref="terra_charts_ref$" />
  </div>
</template>
