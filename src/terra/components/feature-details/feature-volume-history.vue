<!-- eslint-disable vue/prop-name-casing -->
<script setup>
import { computed } from 'vue';
import { $date } from '~/common/utils/date.util';
import { useFeatureActions } from '~/terra/utils/feature-actions.composable.js';

const props = defineProps({
  selected_feature: {
    type: Function,
  },
});

const $t = inject('$t');

const { getAllSelectedFeaturesVolumeHistory, exportVolumeHistory } = useFeatureActions();

const volume_calculation_columns = computed(() => ([
  {
    header: $t('Date'),
    accessorKey: 'date',
    id: 'date',
    size: 100,
  },
  {
    header: $t('Type'),
    accessorKey: 'layer',
    id: 'layer',
    size: 60,
  },
  {
    header: $t('Method'),
    accessorKey: 'method',
    id: 'method',
    size: 140,
  },
  {
    header: $t('Cut'),
    accessorKey: 'cut',
    id: 'cut',
    size: 80,
  },
  {
    header: $t('Fill'),
    accessorKey: 'fill',
    id: 'fill',
    size: 80,
  },
  {
    header: $t('Total'),
    accessorKey: 'total',
    id: 'total',
    size: 80,
  },
  {
    header: $t('Height'),
    accessorKey: 'height',
    id: 'height',
    size: 80,
  },
  {
    header: $t('Net'),
    accessorKey: 'net',
    id: 'net',
    size: 80,
  },
]));

const volume_calculation_history = computed(() => {
  return getAllSelectedFeaturesVolumeHistory([props.selected_feature]);
});
</script>

<template>
  <div v-if="volume_calculation_history.length" class="my-4">
    <div class="flex items-center justify-between mb-2">
      <div class="font-semibold text-base">
        {{ $t('Volume History') }}
      </div>
      <HawkButton type="outlined" @click="exportVolumeHistory">
        {{ $t('Export') }}
      </HawkButton>
    </div>
    <div class="w-full scrollbar">
      <HawkTable
        :data="volume_calculation_history"
        :columns="volume_calculation_columns"
        :manual_pagination="true"
        :show_menu_header="false"
        :header_grid_lines="{
          horizontal: true,
          vertical: true,
        }"
        :data_grid_lines="{
          horizontal: true,
          vertical: true,
        }"
        is_gapless
        disable_resize
        :is_pagination_enabled="false"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  :deep() {
    th {
      font-weight: 600;
      padding: 12px 8px !important;
    }
    td {
      padding: 12px 8px !important;
    }
    tr:last-child  {
      td:first-child{
        border-bottom-left-radius: 8px;
      }
      td:last-child {
        border-bottom-right-radius: 8px;
      }
    }
  }
</style>
