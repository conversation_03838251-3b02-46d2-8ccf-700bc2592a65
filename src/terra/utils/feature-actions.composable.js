import { useClipboard } from '@vueuse/core';
import { omit } from 'lodash-es';
import { useModal } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import EditFeaturePropertiesPopup from '~/terra/components/feature-details/edit-feature-properties-popup.vue';
import FeaturesMovePopup from '~/terra/components/feature-details/features-move-popup.vue';
import ChangeFeatureTypePopup from '~/terra/components/feature-type-groups/change-feature-type-popup.vue';
import { useTerraStore } from '~/terra/store/terra.store';

export function useFeatureActions() {
  const { $t, $toast, $services, route, auth_store, common_store, $date } = useCommonImports();
  const terra_store = useTerraStore();

  const tasks_forms = computed(() => {
    return Object.values(terra_store.selected_tasks_forms).reduce((acc, cur) => {
      if (acc[cur.properties.feature_type]) {
        acc[cur.properties.feature_type].data.push(JSON.parse(cur.properties.data));
        acc[cur.properties.feature_type].count = acc[cur.properties.feature_type].count + 1;
      }
      else {
        acc[cur.properties.feature_type] = {
          data: [JSON.parse(cur.properties.data)],
          count: 1,
        };
      }
      return acc;
    }, {});
  });

  function moveSelectedFeatures(used_for = null) {
    const uid = terra_store.selected_features[0]?.properties?.uid;
    const move_popup = useModal({
      component: FeaturesMovePopup,
      attrs: {
        onClose() {
          move_popup.close();
        },
        onMove() {
          if (used_for)
            terra_store.terra_track_events('Table select row action move', { uid });
          else
            terra_store.terra_track_events('Moved', { uid });
        },
      },
    });
    move_popup.open();
  }

  function deleteSelectedFeatures(used_for = null) {
    const is_bulk_select = terra_store.selected_features.length > 1;
    const delete_popup = useModal({
      component: HawkDeletePopup,
      attrs: {
        header: is_bulk_select ? $t('Delete features') : $t('Delete feature'),
        content: is_bulk_select ? $t('Are you sure you want to delete the features?') : $t('Are you sure you want to delete the feature?'),
        show_toast: false,
        onClose() {
          delete_popup.close();
        },
        confirm: async () => {
          const uid = terra_store.selected_features[0]?.properties?.uid;
          await terra_store.delete_features();
          if (used_for)
            terra_store.terra_track_events('Table select row action delete', { count: terra_store.selected_features.length, uid });
          else
            terra_store.terra_track_events('Feature deleted', { count: terra_store.selected_features.length, uid });
          delete_popup.close();
        },
      },
    });
    delete_popup.open();
  }

  function handleChangeFeatureType(feature_type, handleOnUpdateComplete = null, handleCancel = null) {
    const change_feature_type_popup = useModal({
      component: ChangeFeatureTypePopup,
      attrs: {
        selectedFeatureTypeUid: feature_type.uid,
        clickToClose: false,
        escToClose: false,
        onClose() {
          change_feature_type_popup.close();
          if (handleCancel)
            handleCancel();
        },
        onUpdateComplete() {
          if (handleOnUpdateComplete)
            handleOnUpdateComplete();
        },
      },
    });
    change_feature_type_popup.open();
  }

  async function bulkUpdateNotesAndAttachments(payload) {
    try {
      const project_data = terra_store.selected_features.reduce((acc, feature) => {
        const feature_uid = feature.properties.uid;
        const project = feature.properties.project;
        if (!acc[project]) {
          acc[project] = [];
        }
        acc[project].push(feature_uid);
        return acc;
      }, {});

      const promises = Object.keys(project_data).map(async (project) => {
        const feature_uids = project_data[project];
        const { data } = await $services.features.patch({
          body: feature_uids.map(uid => ({
            uid,
            ...payload,
          })),
          attribute: `container/${route.params.id}/project/${project}`,
        });
        if (data.length) {
          terra_store.update_features_in_state({
            features: data.map(properties => ({ properties })),
            properties_to_update: ['notes', 'attachments'],
          });
          data.forEach((feature) => {
            const feature_uid = feature.uid;
            const project_uid = terra_store.features_hash[feature_uid]?.properties?.project;
            const { project_features_attachment_map } = terra_store.gallery_view_state;
            const project_attachment_details = project_features_attachment_map?.[project_uid];
            const updated_feature = {
              feature_uid,
              project_uid,
              feature_attachments: feature.attachments,
            };

            project_features_attachment_map[project_uid] = {
              ...(project_attachment_details || {}),
              [feature_uid]: {
                ...(project_attachment_details?.[feature_uid] || {}),
                ...updated_feature,
              },
            };

            terra_store.set_gallery_state('project_features_attachment_map', project_features_attachment_map);
          });
        }
      });
      await Promise.all(promises);
      $toast({
        type: 'success',
        text: $t('Notes added successfully'),
        title: $t('Success'),
      });
    }
    catch (error) {
      $toast({
        type: 'error',
        text: $t('Something went wrong'),
        title: $t('Error'),
      });
      logger.log(`Error in bulkUpdateNotesAndAttachments: ${error}`);
    }
  }

  function handleClearSerialNumbers(options = {}) {
    const delete_popup = useModal({
      component: HawkDeletePopup,
      attrs: {
        header: options?.clear_selected ? $t('Clear serial numbers') : $t('Clear all serial numbers'),
        content: $t('Are you sure you want to clear the serial numbers? This action cannot be undone.'),
        button_text: $t('Clear'),
        show_toast: false,
        onClose() {
          delete_popup.close();
        },
        confirm: async () => {
          try {
            let payload = options.payload;
            if (!payload) {
              payload = terra_store.selected_features.reduce((acc, feature) => {
                const feature_uid = feature.properties.uid;
                const feature_details = terra_store.features_hash[feature_uid];
                const project = feature_details?.properties?.project;
                const serial_numbers = feature_details?.properties?.extraProperties?._serial_numbers || {};
                if (Object.keys(serial_numbers).length > 0) {
                  if (!acc[project]) {
                    acc[project] = {};
                  }
                  acc[project][feature_uid] = Object.keys(serial_numbers);
                }
                return acc;
              }, {});
            }
            const promises = Object.keys(payload).map(async (project) => {
              const serial_numbers = payload[project];
              const response = await $services.features.post({
                attribute: `container/${route.params.id}/project/${project}/barcode-clear`,
                body: serial_numbers,
              });
              if (response?.data?.features?.length > 0) {
                Object.entries(serial_numbers).forEach(([feature_uid, serial_numbers]) => {
                  terra_store.feature_duplicates_map[feature_uid] = omit(terra_store.feature_duplicates_map[feature_uid], serial_numbers);
                });
                await terra_store.update_features_in_state({
                  features: response?.data?.features,
                  clearSelectedFeatures: false,
                });
              }
            });
            await Promise.all(promises);
            if (options?.on_submit) {
              options.on_submit();
            }
            $toast({
              title: $t('Success'),
              text: $t('Serial numbers cleared successfully'),
              type: 'success',
            });
          }
          catch (error) {
            logger.error('Error clearing serial numbers:', error);
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again later'),
              type: 'error',
            });
          }

          delete_popup.close();
        },
      },
    });
    delete_popup.open();
  }

  function handleEditFeature() {
    const selected_feature = terra_store.selected_features[0];
    const edit_properties_popup = useModal({
      component: EditFeaturePropertiesPopup,
      attrs: {
        can_modify_properties: terra_store.check_permission('modify_features'),
        can_modify_feature_type: !selected_feature?.properties?.workflow && terra_store.check_permission('modify_feature_properties'),
        properties: selected_feature.properties,
        ftg: [],
        features_object: terra_store.feature_types,
        is_bulk_select: false,
        onClose() {
          edit_properties_popup.close();
        },
        async on_submit(form) {
          try {
            terra_store.selected_features = terra_store.selected_features.map(feature => ({
              ...feature,
              properties: {
                ...feature.properties,
                ...form,
              },
            }),
            );
            await terra_store.create_or_update_selected_features({
              clearSelectedFeatures: false,
              updateFeatureRequest: true,
            });
            $toast({
              title: $t('Success'),
              text: $t('Feature updated successfully'),
              type: 'success',
            });
          }
          catch (error) {
            logger.error('Error updating feature properties:', error);
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again later'),
              type: 'error',
            });
          }
          edit_properties_popup.close();
        },
      },

    });
    edit_properties_popup.open();
  }

  function handleCopy() {
    const { copy } = useClipboard();
    copy(terra_store.selected_features.map(row => row.properties.name || '').join('\n'));
    $toast({
      title: $t('Successfully copied!'),
      text: $t('Feature names are copied to your clipboard.'),
      type: 'success',
      position: 'top-right',
    });
  }

  function handleInverseSerialNumbers() {
    const delete_popup = useModal({
      component: HawkDeletePopup,
      attrs: {
        header: $t('Inverse serial numbers'),
        content: $t('Are you sure you want to inverse the serial numbers? This action cannot be undone.'),
        show_toast: false,
        button_text: $t('Confirm'),
        onClose() {
          delete_popup.close();
        },
        confirm: async () => {
          try {
            const payload = terra_store.selected_features.reduce((acc, feature) => {
              const feature_uid = feature.properties.uid;
              const feature_details = terra_store.features_hash[feature_uid];
              const project = feature_details?.properties?.project;
              if (!acc[project]) {
                acc[project] = { features: [] };
              }
              acc[project].features.push(feature_uid);
              return acc;
            }, {});
            const promises = Object.keys(payload).map(async (project) => {
              const body = payload[project];
              const response = await $services.features.post({
                attribute: `container/${route.params.id}/project/${project}/barcode-inverse`,
                body,
              });
              if (response?.data?.features?.length > 0) {
                await terra_store.update_features_in_state({
                  features: response?.data?.features,
                  clearSelectedFeatures: false,
                });
              }
            });
            await Promise.all(promises);
            $toast({
              title: $t('Success'),
              text: $t('Serial numbers inversed successfully'),
              type: 'success',
            });
          }
          catch (error) {
            logger.error('Error inversing serial numbers:', error);
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again later'),
              type: 'error',
            });
          }

          delete_popup.close();
        },
      },
    });
    delete_popup.open();
  }

  async function handleExportSerialNumbers() {
    const columns = ['Block', 'Tracker name', 'Serial number', 'Module(X,Y)', 'Module number', 'Duplicated at'];

    const data = terra_store.selected_features.map((feature) => {
      const feature_uid = feature.properties.uid;
      const feature_details = terra_store.features_hash[feature_uid];
      const additional_properties = terra_store.get_feature_additional_properties(feature_details);
      const serial_numbers = feature_details?.properties?.extraProperties?._serial_numbers || {};
      return Object.entries(serial_numbers).map(([serial_number_key, serial_number]) => {
        const [x, y] = serial_number_key.split(':');
        const module_x = Number(x) + 1;
        const module_y = Number(y) + 1;
        const serial_number_duplicate = terra_store.feature_duplicates_map[feature_uid]?.[serial_number_key] || {};
        return {
          'Block': additional_properties.Sublayer,
          'Tracker name': feature.properties.name,
          'Serial number': serial_number,
          'Module(X,Y)': `${module_x},${module_y}`,
          'Module number': module_x,
          'Duplicated at': serial_number_duplicate?.duplicated_at || '',
        };
      });
    }).flat();

    const ExcelJS = await import('exceljs');
    const { saveAs } = await import('file-saver');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet($t('Serial numbers'));
    worksheet.columns = columns.map(column => ({ header: column, key: column, width: 20 }));
    worksheet.addRows(data);

    const buffer = await workbook.xlsx.writeBuffer();
    const file_name = terra_store.selected_features[0].properties.name || 'serial_numbers';
    saveAs(new Blob([buffer]), `${file_name}.xlsx`);
    $toast({
      text: $t('Exported XLSX'),
      type: 'success',
    });
  }

  function getVolumeCalculatedData(data, type) {
    const method_value_name = {
      'base-height': 'Base height',
      'flat-minimum': 'Flat minimum',
      'triangulated': 'Triangulated',
      'surface-to-surface': 'Surface to Surface',
    };
    if (type === 'date')
      return $date(data, 'DD-MM-YYYY');
    if (type === 'format number') {
      if (typeof data === 'string')
        data = Number.parseFloat(data);
      return (typeof data === 'number') ? data.toFixed(2) : '-';
    }
    return method_value_name[data];
  }

  function getAllSelectedFeaturesVolumeHistory(selected_features, is_export = false) {
    const all_history = [];

    selected_features.forEach((feature) => {
      const volume_history = feature?.properties?.dataProperties?.volume_cal_history;
      if (Array.isArray(volume_history)) {
        volume_history.forEach((item) => {
          const format = { ...item };
          const extraProperties = terra_store.get_feature_additional_properties(feature);
          format.feature_name = feature?.properties?.name || '';
          format.layer_name = extraProperties.Layer;
          format.sublayer_name = extraProperties.Sublayer;
          format.layer = item.method === 'surface-to-surface' ? '-' : format.layer?.toUpperCase();
          format.actual_date = item.date;
          format.date = is_export ? $date(item.date, 'DD-MM-YYYY HH:mm:ss') : getVolumeCalculatedData(item.date, 'date');
          format.method = getVolumeCalculatedData(item.method, 'method');
          format.height = item.method === 'base-height'
            ? getVolumeCalculatedData(item.height, 'format number')
            : '-';
          format.cut = getVolumeCalculatedData(item.cut, 'format number');
          format.fill = getVolumeCalculatedData(item.fill, 'format number');
          format.total = getVolumeCalculatedData(item.total, 'format number');
          const net = (typeof item?.cut === 'number' && typeof item?.fill === 'number')
            ? (item.cut - item.fill)
            : '';
          format.net = getVolumeCalculatedData(net, 'format number');
          all_history.push(format);
        });
      }
    });

    // Optional: sort by date
    all_history.sort((a, b) => {
      const date_a = new Date(a.actual_date).getTime();
      const date_b = new Date(b.actual_date).getTime();
      return date_b - date_a;
    });

    return all_history;
  }

  async function exportVolumeHistory() {
    const ExcelJS = await import('exceljs');
    const { saveAs } = await import('file-saver');

    const columns = [
      { header: 'Name', key: 'feature_name', width: 20 },
      { header: 'Layer', key: 'layer_name', width: 15 },
      { header: 'Sublayer', key: 'sublayer_name', width: 15 },
      { header: 'Date', key: 'date', width: 15 },
      { header: 'Type', key: 'layer', width: 15 },
      { header: 'Method', key: 'method', width: 15 },
      { header: 'Cut(m³)', key: 'cut', width: 15 },
      { header: 'Fill(m³)', key: 'fill', width: 15 },
      { header: 'Total(m³)', key: 'total', width: 15 },
      { header: 'Height(m)', key: 'height', width: 15 },
      { header: 'Net(m³)', key: 'net', width: 15 },
    ];

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Volume History');
    worksheet.columns = columns;

    getAllSelectedFeaturesVolumeHistory(terra_store.selected_features, true).forEach((row) => {
      worksheet.addRow(row);
    });
    const buffer = await workbook.xlsx.writeBuffer();
    const filename = `${auth_store.current_organization.name} - ${common_store.assets_map[route.params.asset_id]?.name} - ${terra_store.container.name} - ${new Date()}`;
    saveAs(new Blob([buffer]), `${filename}.xlsx`);
  }

  return {
    tasks_forms,
    deleteSelectedFeatures,
    moveSelectedFeatures,
    handleChangeFeatureType,
    bulkUpdateNotesAndAttachments,
    handleInverseSerialNumbers,
    handleClearSerialNumbers,
    handleEditFeature,
    handleCopy,
    handleExportSerialNumbers,
    exportVolumeHistory,
    getAllSelectedFeaturesVolumeHistory,
  };
}
