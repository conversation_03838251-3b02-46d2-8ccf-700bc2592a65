import dayjs from 'dayjs';
import { cloneDeep, groupBy, orderBy } from 'lodash-es';
import { useAuthStore } from '~/auth/stores/auth.store';
import { usePdfExporter } from '~/common/composables/pdf-exporter.js';
import { useCommonStore } from '~/common/stores/common.store.js';
import { useTerraStore } from '~/terra/store/terra.store';

export function useFeatureAttachmentsPdfExporter({ isExporting = () => {}, updatePdfExportProgress = () => {} }) {
  const pdf_exporter = usePdfExporter();

  const common_store = useCommonStore();
  const auth_store = useAuthStore();
  const terra_store = useTerraStore();

  const state = reactive({
    attachments: [],
    filter: null,
    available_height: 0,
  });
  const generated_by = common_store.get_user_or_team_name(auth_store?.logged_in_user_details?.user_id);
  const date_now_local = (new Date()).toLocaleString();
  const current_organization = auth_store.current_organization;
  const current_organization_name = current_organization.name;
  const current_asset_name = common_store.active_asset.name;

  async function downloadPDF(generated_blob, project_names) {
    let filename = `${current_organization_name} - ${current_asset_name} - ${terra_store.container.name} - ${project_names}`;

    filename = filename.replace(/[^\w ]+/g, '').replace(/ +/g, '-');
    filename = `${filename}.pdf`;

    if (!isExporting())
      throw new Error('PDF export stopped!');

    const anchor = document.createElement('a');
    document.body.appendChild(anchor);
    anchor.href = window.URL.createObjectURL(generated_blob);
    anchor.download = filename;
    anchor.click();
  }

  async function createHeader(content) {
    const header = { alignment: 'justify', columns: [] };

    const orgLogoDataURL = await pdf_exporter.getOrgLogo(false);

    if (orgLogoDataURL) {
      header.columns = [
        {
          image: orgLogoDataURL,
          width: 120,
          height: 40,
        },
      ];
    }
    else {
      header.columns = [{
        image: await pdf_exporter.getAppLogo(),
        width: 120,
        height: 40,
      }];
    }
    header.columns.push({
      width: '*',
      alignment: 'right',
      stack: [
        { text: 'Photo Report', bold: true, fontSize: 16, margin: [0, 0, 0, 20] },
        { text: [{ text: 'Asset', bold: true }, `: ${current_asset_name}`], margin: [0, 2, 0, 0] },
        { text: common_store.active_asset.address?.name || '', margin: [0, 2, 0, 0] },
        state.filter && { text: [{ text: 'Date range', bold: true }, `: ${state.filter.value[0].format('MMM DD, YYYY')} - ${state.filter.value[1].format('MMM DD, YYYY')}`], margin: [0, 2, 0, 0] },
      ],
    });

    content.push(header);

    content.push({
      canvas: [
        {
          type: 'line',
          x1: 0,
          y1: 0,
          x2: 515,
          y2: 0,
          lineWidth: 1,
          lineColor: '#cccccc',
        },
      ],
      margin: [0, 10, 0, 10],
    });
  }

  async function addPageBreak(content) {
    content.push({ text: '', pageBreak: 'before' });
    state.available_height = 760; // Default A4 height after margins

    await createHeader(content);
    state.available_height -= 80; // Header height (removing generated by and generated on)
  }

  async function addImages(content, images_list) {
    for (let i = 0; i < images_list.length; i += 6) {
      const row = images_list.slice(i, i + 6);

      if (state.available_height < 155) {
        // Create a new page if the available height is less than 145
        await addPageBreak(content);
      }

      // Each row for attachments takes 145 height
      const columns = row.map(item => ({
        width: 75,
        stack: [
          {
            text: item.config.feature_name,
            alignment: 'left',
            fontSize: 8,
            bold: true,
            margin: [0, 0, 0, 3],
          },
          {
            image: item.image_src,
            width: 75,
            height: 75,
          },
          {
            text: item.config.label,
            link: item.config.url,
            alignment: 'left',
            fontSize: 8,
            color: 'blue',
            margin: [0, 5, 0, 0],
          },
        ],
      }));

      content.push({ columns, margin: [0, 10, 0, 10], columnGap: 10 });
      state.available_height -= 155;
    }
  }
  async function addProjectData(content, children) {
    for (let i = 0; i < children.length; i++) {
      const data = children[i];

      if (state.available_height < 150) {
        // Create a new page if the available height is less than 150
        await addPageBreak(content);
      }

      // Create a new section for each note attachment label takes 25 height
      const section = {
        stack: [
          {
            text: data.label,
            bold: true,
            fontSize: 12,
            margin: [0, 5, 0, 5],
          },
        ],
      };
      state.available_height -= 25;

      const attachments = data.attachments;

      if (!isExporting())
        throw new Error('PDF export stopped!');

      const images_list = await Promise.all(attachments.map(async (attachment) => {
        const image_src = await pdf_exporter.imageToDataURL(attachment.config.preview);
        return { image_src, config: attachment.config };
      }));

      await addImages(section.stack, images_list);
      content.push(section);
    }
  }
  async function addAttachments(content, attachments) {
    for (let i = 0; i < attachments.length; i++) {
      const data = attachments[i];

      if (state.available_height < 210) {
        // Create a new page if the available height is less than 210
        await addPageBreak(content);
      }

      // Create a new section for each project take 50 height
      const section = {
        stack: [
          {
            table: {
              widths: ['*'],
              body: [
                [
                  {
                    text: data.label,
                    margin: [5, 5, 5, 5],
                    fillColor: '#c9dbf9',
                    bold: true,
                    fontSize: 12,
                  },
                ],
              ],
            },
            layout: 'noBorders',
            margin: [0, 10, 0, 10],
          },
        ],
      };
      state.available_height -= 50;

      await addProjectData(section.stack, data.children);
      content.push(section);
    }
  }

  function packByAttachmentCount(items, limit = 500) {
    const groups = [];

    for (const item of items) {
      let placed = false;

      for (const group of groups) {
        const total = group.reduce((sum, i) => sum + i.attachment_count, 0);
        if (total + item.attachment_count <= limit) {
          group.push(item);
          placed = true;
          break;
        }
      }

      if (!placed) {
        groups.push([item]);
      }
    }

    return groups;
  }

  async function createPDF() {
    const config = {
      defaultStyle: {
        font: 'Roboto',
        fontSize: 10,
      },
      page_margins: [40, 40, 40, 40],
      generated_by,
      generated_on: date_now_local,
      header: {},
      footer: {},
    };
    state.first_page = true;

    if (!isExporting())
      throw new Error('PDF export stopped!');

    const { wrapped_worker, worker } = await pdf_exporter.createPDFGeneratorWebWorker();

    try {
      const header_content = [];
      await createHeader(header_content);

      header_content.push({
        absolutePosition: { x: 40, y: 780 },
        columns: [
          { text: [{ text: 'Generated by', bold: true }, `: ${generated_by}`], margin: [0, 2, 0, 0] },
          { text: [{ text: 'Generated on', bold: true }, `: ${date_now_local}`], margin: [0, 2, 0, 0], alignment: 'right', width: '*' },
        ],
      });

      const grouped_attachments = packByAttachmentCount(state.attachments);
      for (let i = 0; i < grouped_attachments.length; i++) {
        state.available_height = 760; // Default A4 height after margins
        updatePdfExportProgress({
          current_export_file_index: i + 1,
          total_files_to_export: grouped_attachments.length,
        });
        const content = [...header_content];
        state.available_height -= 80; // Header height
        await addAttachments(content, grouped_attachments[i]);
        logger.log(content);

        if (!isExporting())
          throw new Error('PDF export stopped!');

        const project_names = grouped_attachments[i].map(x => x.project_name).join(', ');
        const generated_blob = await wrapped_worker.generatePDF(content, [], config);
        downloadPDF(generated_blob, project_names);
      }
    }
    catch (error) {
      logger.error(error);
    }
    finally {
      worker?.terminate();
    }
  }

  function getFormattedAttachments(attachments) {
    const attachments_copy = cloneDeep(attachments).map((attachment) => {
      const feature_name = terra_store.features_hash[attachment.feature_uid]?.name;

      const formatted_date = attachment.created_at
        ? dayjs(attachment.created_at).format('DD MMMM YYYY, h:mm a').replace('AM', 'am').replace('PM', 'pm')
        : attachment?.file_name || feature_name || 'Attachment';
      const url = new URL(attachment?.url || attachment?.service?.url);
      url.searchParams.delete('f');

      return {
        ...attachment,
        config: {
          label: formatted_date,
          preview: attachment.meta?.thumbnails?.small || attachment?.thumbnails?.small || attachment?.thumbnail_small || attachment?.url,
          url: url.toString(),
        },
      };
    }).filter((attachment) => {
      const ext = (attachment?.file_name || '').split('.').pop().toLowerCase();
      const supported_formats = ['jpg', 'jpeg', 'png'];
      return !attachment.file_name || supported_formats.includes(ext);
    });
    const group_by_projects = groupBy(orderBy(attachments_copy, ['created_at'], ['asc']), 'project_uid');

    const data = Object.entries(group_by_projects).map(([project_uid, project_attachments]) => {
      const project = terra_store.active_projects_data_map({ all_projects: true })[project_uid];
      const group = terra_store.container.groups[project?.group];

      const grouped_attachments = groupBy(project_attachments, (attachment) => {
        if (attachment.attached_to?.type === 'notes')
          return 'notes';
        else if (attachment.current_feature_type)
          return attachment.current_feature_type;
        const feature = terra_store.features_hash[attachment.feature_uid];
        return feature?.properties?.featureType;
      });

      const children = Object.entries(grouped_attachments).map(([feature_type_uid, attachments]) => {
        const feature_type = terra_store.feature_types_by_uid[feature_type_uid];
        const feature_type_group = terra_store.ftg_map[feature_type?.featureTypeGroup];
        // Add feature name to attachments and order them by date and feature name
        const updated_attachments = orderBy(attachments.map((attachment) => {
          const feature = terra_store.features_hash[attachment.feature_uid];
          const feature_name = feature?.properties?.name || 'Untitled Feature';
          return {
            ...attachment,
            config: {
              ...attachment.config,
              feature_name,
            },
            feature_name,
            label: attachment.config.label,
          };
        }), ['feature_name', 'label'], ['asc']);
        return {
          label: feature_type_uid === 'notes' ? 'Notes' : feature_type?.name ? `${feature_type_group?.name} > ${feature_type?.name}` : 'Unassociated',
          attachments: updated_attachments,
          order_index: feature_type?.order_index,
        };
      });

      const sorted_children = orderBy(children, [(child) => {
        if (child.label === 'Notes')
          return 3;
        if (child.label === 'Unassociated')
          return 2;
        return 1;
      }, 'order_index'], ['asc']);

      return {
        project_name: project.name,
        group_name: group.name,
        label: `Layer : ${group.name} > ${project.name}`,
        children: sorted_children,
        attachment_count: project_attachments.length,
      };
    });
    return data;
  }

  async function exportPDF(attachments, filter) {
    state.attachments = getFormattedAttachments(attachments);
    state.filter = filter;

    if (!isExporting())
      return;

    await createPDF();

    state.attachments = [];
    state.filter = null;

    return Promise.resolve('download complete');
  }
  return { exportPDF };
}
