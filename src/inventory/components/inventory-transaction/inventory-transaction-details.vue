<script setup>
import { useModal } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import TableWrapperVue from '~/common/components/organisms/hawk-table/table.wrapper.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { renderAsTextarea } from '~/common/utils/common.utils';
import FormCompactView from '~/forms/components/form-compact-view.vue';
import InventoryCustomField from '~/inventory/components/inventory-custom-fields/inventory-custom-field.vue';
import InventoryTransactionAssociatedStatus from '~/inventory/components/inventory-transaction/inventory-transaction-associated-status.vue';
import InventoryTransactionAssociationDisassociation from '~/inventory/components/inventory-transaction/inventory-transaction-association-disassociation.vue';
import InventoryTransactionDetailsSidebar from '~/inventory/components/inventory-transaction/inventory-transaction-details-sidebar.vue';
import InventoryTransactionEditCustomField from '~/inventory/components/inventory-transaction/inventory-transaction-edit-custom-field.vue';
import InventoryTransactionEmail from '~/inventory/components/inventory-transaction/inventory-transaction-email.vue';
import InventoryTransactionExport from '~/inventory/components/inventory-transaction/inventory-transaction-export.vue';
import InventoryTransactionShare from '~/inventory/components/inventory-transaction/inventory-transaction-share.vue';
import InventoryTransactionTracking from '~/inventory/components/inventory-transaction/inventory-transaction-tracking.vue';
import InventoryValidInvalidStocksPopup from '~/inventory/components/inventory-transaction-form/inventory-invalid-valid-popup.vue';
import InventoryUom from '~/inventory/components/inventory-uom/inventory-uom.vue';
import { useCustomFields } from '~/inventory/composables/inventory-custom-fields.composable.js';
import { useStockItems } from '~/inventory/composables/inventory-stock-items.composable.js';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';

const { $t, $date, $services, $toast, $number, auth_store, route, router } = useCommonImports();
const inventory_store = useInventoryStore();
const { prepareValidInvalidStocksData } = useStockItems();
const { copy, copied } = useClipboard();
const { sort_custom_fields } = useCustomFields();

const columns = [
  { header: $t('Name'), accessorKey: 'name', id: 'name', cell: info => info.getValue(), size: 250 },
  { header: $t('Notes'), accessorKey: 'description', id: 'description', cell: info => info.getValue() || '-', size: 250 },
  { header: $t('Quantity'), accessorKey: 'quantity', id: 'quantity', size: 100 },
];
const is_loading = ref(false);
const show_activities = ref(false);
const state = reactive({
  is_stock_snapshot_loading: false,
  is_status_loading: false,
});

const transaction_details = computed(() => inventory_store.transaction_details);
const organization_cover_image_url = computed(() => auth_store?.current_organization?.cover_image?.url);
const get_sorted_custom_fields = computed(() => {
  const get_adjustment_custom_fields = inventory_store.get_custom_fields({ uid: transaction_details.value.workflow }, true);
  if (get_adjustment_custom_fields?.length)
    return sort_custom_fields(transaction_details.value?.custom_fields, get_adjustment_custom_fields.map(cf => cf.uid));
  return [];
});

const invalid_valid_stocks_popup = useModal({
  component: InventoryValidInvalidStocksPopup,
  attrs: {
    onCancel() {
      invalid_valid_stocks_popup.close();
    },
  },
});

const { open: openDeletePopup, close: closeDeletePopup, patchOptions } = useModal({ component: HawkDeletePopup });

function transactionDeleteHandler() {
  patchOptions({
    attrs: {
      header: `${$t('Delete Transaction')}`,
      content: `${transaction_details.value.form_submissions?.length ? 'There is a form associated with this transaction. ' : ''}Are you sure you want to delete the transaction?`,
      match_text: transaction_details.value.number,
      match_text_input_placeholder: 'Enter the name of the transaction to delete',
      onClose: () => closeDeletePopup(),
      confirm: async () => {
        try {
          await $services.inventory_adjustments.delete({ id: transaction_details.value.uid });
          delete inventory_store.adjustments_map[transaction_details.value.uid];
          inventory_store.active_transaction_uid = (inventory_store.adjustments?.length && inventory_store.adjustments?.[0].uid) || '';
          closeDeletePopup();
        }
        catch ({ data: error }) {
          const { title, message } = inventory_store.get_error_status(error?.error_code) || {};
          $toast({
            title: title || $t('Something went wrong'),
            text: message || $t('Please try again'),
            type: 'error',
          });
        }
      },
    },
  });
  openDeletePopup();
}

const { open: openTransactionTracking, close: closeTransactionTracking, patchOptions: patchTransactionTracking } = useModal({ component: InventoryTransactionTracking });
function transactionTrackingHandler() {
  patchTransactionTracking({
    attrs: {
      transactionDetails: transaction_details.value,
      onClose: () => closeTransactionTracking(),
    },
  });
  openTransactionTracking();
}

function getCustomField(uid) {
  return inventory_store?.custom_fields_map[uid] || {};
}
function getCustomFieldValue(uid) {
  return transaction_details.value?.custom_fields?.find(field => field.uid === uid)?.value;
}
function getWorkflow(workflow_uid) {
  return inventory_store.workflows_map[workflow_uid];
}
function handleCreateAssociation(associated_transaction) {
  router.push({
    name: 'inventory-transaction-form',
    params: {
      ...route.params,
      workflow_id: associated_transaction?.associated_item_workflow,
    },
    query: {
      associated_transaction: transaction_details.value?.uid,
    },
  });
}

async function getDetails(update = false, loader = true) {
  try {
    is_loading.value = loader;
    await inventory_store.set_transaction_details({
      id: inventory_store.active_transaction_uid,
      query: {
        asset: route.params.asset_id,
      },
    });
    if (update)
      inventory_store.adjustments_map[inventory_store.active_transaction_uid] = transaction_details.value;
    is_loading.value = false;
  }
  catch (error) {
    is_loading.value = false;
    logger.error('[DEBUG] inventory-transaction-details.vue::64\n', error);
  }
}

function getAttachments(attachments) {
  return attachments?.map(attachment => ({
    ...attachment,
    file_name: attachment.file_name || attachment.name || attachment.service.key.split('/').pop(),
  }));
}

watch(() => inventory_store.active_transaction_uid, () => {
  getDetails();
  show_activities.value = false;
}, { immediate: true });

async function openStockSnapshot(data) {
  try {
    state.is_stock_snapshot_loading = data.uid;
    const response = await $services.inventory_adjustments.get({
      attribute: `items/${data.uid}/stock`,
      id: transaction_details.value?.uid,
    });
    if (response.data?.stock_snapshot?.length) {
      const { invalid_stocks, valid_stocks } = prepareValidInvalidStocksData(response.data?.stock_snapshot);
      invalid_valid_stocks_popup.patchOptions({
        attrs: {
          invalid_stocks,
          valid_stocks,
          workflow: inventory_store.workflows_map[transaction_details.value?.workflow],
          item: inventory_store.items_map[data.uid],
          is_batch_number: inventory_store.items_map[data.uid]?.is_batch_number,
          stock_input: inventory_store.transaction_details.adjustment_lines_map[data.uid]?.stock_input,
          onClose() {
            invalid_valid_stocks_popup.close();
          },
        },
      });
      state.is_stock_snapshot_loading = null;
      invalid_valid_stocks_popup.open();
    }
    else { state.is_stock_snapshot_loading = null; }
  }
  catch (err) {
    logger.error('🚀 ~ openStockSnapshot ~ err:', err);
    state.is_stock_snapshot_loading = null;
  }
}

function getLocationDetails({ uid, type }) {
  return inventory_store.get_location_details({ uid, type });
}

async function markAsFulfilled(is_fulfilled) {
  try {
    state.is_status_loading = true;
    const { data } = await $services.inventory_adjustments.patch({
      id: transaction_details.value?.uid,
      body: {
        reconciliation: { is_fulfilled },
      },
    });
    if (data?.adjustment?.uid) {
      await getDetails(false, false);
      inventory_store.adjustments_map[data.adjustment.uid] = data.adjustment;
      $toast({
        title: 'Marked as fulfilled',
        text: 'Marked as fulfilled successfully',
        type: 'success',
      });
      state.is_status_loading = false;
    }
  }
  catch (error) {
    state.is_status_loading = false;
    logger.log('🚀 ~ markAsFulfilled ~ error:', error);
  }
}

watch(() => copied.value, (value) => {
  if (value) {
    $toast({
      title: $t('Copied successfully'),
      text: $t('The item number has been copied successfully.'),
      type: 'success',
    });
  }
});
</script>

<template>
  <HawkLoader v-if="is_loading" />
  <div v-else>
    <div v-if="transaction_details?.associated_transaction_types?.length"
      class="px-6 py-4 flex justify-between items-center">
      <InventoryTransactionAssociationDisassociation :transaction-details="transaction_details"
        @update="getDetails(true)" />
      <div class="flex gap-2 flex-shrink-0">
        <HawkButton icon type="text" @click="show_activities = !show_activities;">
          <IconHawkClockHistory />
        </HawkButton>
        <HawkButton type="link" @click="transactionTrackingHandler">
          {{ $t('Track transactions') }}
        </HawkButton>
      </div>
    </div>
    <hr>
    <div class="grid gap-6 p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center pb-2 gap-3">
          <div class="text-lg font-semibold">
            {{ transaction_details?.number }}
          </div>
          <IconHawkCopyThree class="text-gray-400 cursor-pointer hover:text-gray-700"
            @click="copy(transaction_details?.number)" />
          <HawkBadge v-if="transaction_details?.status === 'draft'" type="dark" color="orange">
            {{ $t('Draft') }}
          </HawkBadge>
          <HawkLoader v-if="state.is_status_loading" container_class="" height="5" width="5" />
          <template v-else>
            <InventoryTransactionAssociatedStatus v-if="transaction_details?.associated_transaction_types?.length"
              :reconciliation="{ ...transaction_details?.reconciliation, reconciliation_status: transaction_details?.reconciliation_status }"
              :allow-manual-fulfillment="inventory_store.workflows_map[transaction_details?.workflow]?.allow_manual_fulfillment"
              @mark-as-fulfilled="markAsFulfilled($event)" />
          </template>
        </div>
        <div class="flex gap-2">
          <HawkButton v-if="transaction_details?.permissions?.update && route?.params?.asset_id" icon type="text"
            @click="router.push({
              name: 'inventory-transaction-form',
              params: {
                ...route.params,
                workflow_id: transaction_details?.workflow,
                transaction_id: transaction_details.uid,
              },
            })">
            <IconHawkPencilOne />
          </HawkButton>
          <InventoryTransactionEditCustomField :update-details="getDetails" />
          <InventoryTransactionShare :transaction-details="transaction_details" />
          <InventoryTransactionExport v-if="transaction_details?.status !== 'draft'"
            :transaction="transaction_details" />
          <InventoryTransactionEmail v-if="transaction_details?.status !== 'draft'" />
          <HawkButton v-if="transaction_details?.permissions?.destroy" icon type="text"
            @click="transactionDeleteHandler">
            <IconHawkTrashFour />
          </HawkButton>
        </div>
      </div>
      <div class="flex justify-between">
        <div>
          <img v-if="organization_cover_image_url?.length" :src="organization_cover_image_url" alt="org logo"
            class="object-contain w-24 h-10">
          <div v-else>
            <img src="../../../../assets/logos/taskmapper-full.svg" alt="TaskMapper" width="133">
          </div>
        </div>
        <div class="text-sm grid gap-2 text-right">
          <div v-if="transaction_details?.workflow?.length" class="text-lg font-semibold">
            {{ inventory_store.workflows_map[transaction_details?.workflow]?.name }}
          </div>
          <div>{{ transaction_details?.number }} </div>
          <div>{{ $date(transaction_details?.date, 'DD MMMM YYYY h:mm A') }}</div>
          <div>
            <span>{{ $t('Created by') }}: </span>
            <HawkMembers :members="transaction_details?.created_by" type="label" :has_avatar="false"
              name_classes="!font-normal" :name_truncate_length="20" />
          </div>
        </div>
      </div>
      <div class="flex items-start justify-between gap-10">
        <div class="text-sm grid gap-2">
          <div class="text-lg font-semibold">
            {{ inventory_store.workflows_map[transaction_details?.workflow]?.stock_operation === 'no_op' ?
              $t('Location') :
            $t('From') }}
          </div>
          <div>
            <div class="break-all">
              {{ getLocationDetails(transaction_details?.from_stock)?.name || '-' }}
            </div>
            <div class="text-xs text-gray-600"
              v-html="renderAsTextarea(getLocationDetails(transaction_details?.from_stock)?.description)" />
          </div>
        </div>
        <div v-if="inventory_store.workflows_map[transaction_details?.workflow]?.stock_operation !== 'no_op'"
          class="text-sm grid gap-2 text-right">
          <div class="text-lg font-semibold">
            {{ $t('To') }}
          </div>
          <div>
            <div class="break-all">
              {{ getLocationDetails(transaction_details?.to_stock)?.name }}
            </div>
            <div v-if="transaction_details?.to_stock.type === 'asset'" class="text-xs text-gray-600">
              <p
                v-if="getLocationDetails(transaction_details?.to_stock)?.code || getLocationDetails(transaction_details?.to_stock)?.address?.name">
                <span>
                  {{ getLocationDetails(transaction_details?.to_stock)?.code ?
                    `#${getLocationDetails(transaction_details?.to_stock)?.code}` : '' }}
                </span>
                <span
                  v-if="getLocationDetails(transaction_details?.to_stock)?.code && getLocationDetails(transaction_details?.to_stock)?.address?.name">
                  , </span>
                <span class="font-normal">
                  <HawkText :content="getLocationDetails(transaction_details?.to_stock)?.address?.name" :length="64" />
                </span>
              </p>
            </div>
            <div v-else class="text-xs text-gray-600"
              v-html="renderAsTextarea(getLocationDetails(transaction_details?.to_stock)?.description)" />
          </div>
        </div>
      </div>
      <div id="transaction-stock">
        <TableWrapperVue v-if="transaction_details?.adjustment_line_item_quantity?.length" :height="500"
          container_class="border-0">
          <HawkTable :data="transaction_details.adjustment_line_item_quantity" :columns="columns" is_gapless
            disable_resize :show_menu_header="false">
            <template #name="{ data }">
              <div>
                <div v-if="data.row.original?.number" class="text-xs text-gray-500">
                  #{{ data.row.original?.number }}
                </div>
                <div class="break-all font-medium">
                  {{ data.getValue() }}
                </div>
              </div>
            </template>
            <template #quantity="{ data }">
              <div class="flex items-center gap-2">
                <span>
                   <InventoryQuantity :quantity="data.getValue()" :uom="data.row.original?.uom" />
                </span>
                <HawkButton v-if="data.row.original?.is_serial_number" v-tippy="'View stock'" icon type="text"
                  :loading="state.is_stock_snapshot_loading === data.row.original?.uid"
                  @click="openStockSnapshot(data.row.original)">
                  <IconHawkInfoCircle class="h-4 w-4" />
                </HawkButton>
                <InventoryTransactionAssociatedStatus v-if="transaction_details?.associated_transaction_types?.length"
                  :reconciliation="data.row.original?.reconciliation" status-key="remaining" :show-percentage="false"
                  show-number />
              </div>
            </template>
          </HawkTable>
        </TableWrapperVue>
      </div>
      <div class="flex justify-end">
        <div class="text-sm text-gray-900 flex items-center gap-2">
          <span class="font-semibold mr-3">{{ $t('Total qty') }}:</span>
          <span class="font-semibold">{{ $number(transaction_details?.total_quantity) }}</span>
          <InventoryTransactionAssociatedStatus v-if="transaction_details?.associated_transaction_types?.length"
            :reconciliation="transaction_details?.reconciliation" status-key="remaining" :show-percentage="false"
            show-number />
        </div>
      </div>
      <div v-if="transaction_details?.remarks" class="text-sm mb-6">
        <div class="shrink-0 text-gray-700 font-semibold">
          {{ $t('Remarks') }}
        </div>
        <div class="w-full break-all" v-html="renderAsTextarea(transaction_details?.remarks)" />
      </div>
      <div v-if="transaction_details?.attachments?.length" class="mb-6">
        <div class="font-semibold mb-3 text-sm">
          {{ $t('Attachments') }} ({{ transaction_details?.attachments?.length }})
        </div>
        <HawkAttachmentsGrid :items="getAttachments(transaction_details?.attachments)" :can_delete="false"
          :enable_description="true" :show_delete="false" />
      </div>
      <div>
        <div v-for="custom_field in get_sorted_custom_fields" :key="custom_field.uid" class="text-sm mb-6">
          <div class="shrink-0 text-gray-700 font-semibold">
            {{ getCustomField(custom_field.uid)?.name || 'NA' }}
          </div>
          <InventoryCustomField :value="custom_field.value" :field="getCustomField(custom_field.uid)" />
        </div>
        <div v-if="transaction_details?.form_submissions?.length">
          <div class="text-gray-900 font-semibold text-sm my-4">
            {{ $t('Forms') }}
          </div>
          <FormCompactView :id="`forms-${transaction_details.uid}`" :store_key="`forms-${transaction_details.uid}`"
            wrapper_class="max-h-96 scrollbar" :height="384" :options="{
              show_no_data: true,
              query: {
                form_uid: transaction_details?.form_submissions.map(f => f.form_uid),
              },
            }" />
        </div>
      </div>
      <hr>
      <div v-if="transaction_details?.status === 'published'" class="grid gap-4">
        <div v-for="transaction_type in transaction_details?.associated_transaction_types" :key="transaction_type.uid">
          <HawkAlertBanner color="gray">
            <template #left>
              <div class="flex text-sm gap-1 items-center">
                <div>{{ getCustomField(transaction_type.eta_custom_field)?.name }}:</div>
                <div>{{ getCustomFieldValue(transaction_type.eta_custom_field) ?
                  $date(getCustomFieldValue(transaction_type.eta_custom_field), 'DD MMM, YYYY') : '-' }}</div>
              </div>
            </template>
            <template #right>
              <HawkButton type="outlined" color="active" @click="handleCreateAssociation(transaction_type)">
                Create {{ getWorkflow(transaction_type.associated_item_workflow)?.name?.toLowerCase() }}
              </HawkButton>
            </template>
          </HawkAlertBanner>
        </div>
      </div>
    </div>
  </div>
  <InventoryTransactionDetailsSidebar v-if="show_activities" v-click-outside="() => show_activities = false"
    :uid="transaction_details?.uid" @close="show_activities = false" />
</template>

<style lang="scss">
#transaction-stock {
  table {
    th {
      font-weight: 700;
      background-color: #667085;
      color: #fff;
      height: 44px;
      padding-top: 0;
      padding-bottom: 0;
    }
  }
}
</style>
