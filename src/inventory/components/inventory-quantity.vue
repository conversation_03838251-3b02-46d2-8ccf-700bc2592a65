<script setup>
import { useCommonImports } from '~/common/composables/common-imports.composable';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';
const { $number } = useCommonImports();
defineProps({
  quantity: { default: 0, type: Number },
  uom: { default: '', type: String },
});

const inventory_store = useInventoryStore();
</script>

<template>
  <div class="inline-flex">
    {{ $number(quantity) }} {{ inventory_store?.uom_map?.[uom]?.symbol || '' }}
  </div>
</template>
